from uuid import UUID

from sqlalchemy import delete, select
from sqlalchemy.ext.asyncio import AsyncSession

from constants.extracted_data import DataSourceType
from exceptions import EntityNotFoundError
from models.qual_extracted_data import QualExtractedData
from schemas import ExtractedData

from .conversation import ConversationRepository


__all__ = ['ExtractedDataRepository']


class ExtractedDataRepository:
    """Repository for extracted data-related database operations."""

    def __init__(self, db_session: AsyncSession, conversation_repository: ConversationRepository):
        self.db_session = db_session
        self.conversation_repository = conversation_repository

    async def create(self, conversation_id: UUID, data_source_type: DataSourceType):
        """
        Create a new extracted data record.

        Args:
            conversation_id: The ID of the conversation
            data_source_type: The type of source data

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        extracted_data = QualExtractedData(
            QualConversationId=conversation_internal_id,
            DataSourceType=data_source_type,
        )
        self.db_session.add(extracted_data)
        await self.db_session.flush()

    async def get(self, conversation_id: UUID, data_source_type: DataSourceType) -> ExtractedData | None:
        """
        Get extracted data.

        Args:
            conversation_id: Public conversation ID
            data_source_type: Type of source data to retrieve

        Returns:
            The extracted data if found, None otherwise
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        result = (
            await self.db_session.execute(
                select(QualExtractedData).where(
                    QualExtractedData.QualConversationId == conversation_internal_id,
                    QualExtractedData.DataSourceType == data_source_type,
                )
            )
        ).scalar_one_or_none()
        if not result:
            return None

        result.ConversationPublicId = conversation_id
        return ExtractedData.model_validate(result)

    async def update(self, extracted_data: ExtractedData):
        """
        Upsert extracted data of a specified source type for a conversation.
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(extracted_data.conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(extracted_data.conversation_id))

        extracted_data_db = (
            await self.db_session.execute(
                select(QualExtractedData).where(
                    QualExtractedData.QualConversationId == conversation_internal_id,
                    QualExtractedData.DataSourceType == extracted_data.data_source_type,
                )
            )
        ).scalar_one_or_none()

        if extracted_data_db:
            for key, val in extracted_data.model_dump_for_db().items():
                setattr(extracted_data_db, key, val)
        else:
            extracted_data_db = QualExtractedData(**extracted_data.model_dump_for_db())
            if extracted_data_db.QualConversationId is None:
                extracted_data_db.QualConversationId = conversation_internal_id
            self.db_session.add(extracted_data_db)

        await self.db_session.flush()

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all extracted data associated with a conversation.

        Args:
            conversation_id: The ID of the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        conversation_internal_id = await self.conversation_repository.get_internal_id(conversation_id)
        if conversation_internal_id is None:
            raise EntityNotFoundError('Conversation', str(conversation_id))

        query = delete(QualExtractedData).where(QualExtractedData.QualConversationId == conversation_internal_id)
        await self.db_session.execute(query)
        await self.db_session.flush()
