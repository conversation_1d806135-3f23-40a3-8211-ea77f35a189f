from abc import ABC, abstractmethod

from constants.extracted_data import FieldStatus
from schemas import AggregatedData, FieldHandlerResponse
from schemas.confirmed_data import ConfirmedData


__all__ = [
    'ClientNameHandler',
    'LDMFCountryHandler',
    'DateIntervalsHandler',
    'ObjectiveHandler',
    'OutcomesHandler',
]


class BaseFieldHandler(ABC):
    """
    Abstract base class for handling missing required fields and generating prompts.
    """

    @abstractmethod
    def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> FieldHandlerResponse:
        """
        Checks if the specific field needs confirmation and returns structured response.

        Args:
            aggregated_data: The AggregatedData object to check.
            confirmed_data: The ConfirmedData object with user confirmations.

        Returns:
            FieldHandlerResponse: Structured response indicating next steps.
        """
        pass


class ClientNameHandler(BaseFieldHandler):
    """Handler for RequiredField.CLIENT_INFO."""

    def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> FieldHandlerResponse:
        return FieldHandlerResponse(
                needs_confirmation=False,
                field_status=FieldStatus.MISSING,
                system_message="Implement client name handler",
                next_expected_field=None,
            )


class LDMFCountryHandler(BaseFieldHandler):
    """Handler for RequiredField.LDMF_COUNTRY."""

    def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> FieldHandlerResponse:
        return FieldHandlerResponse(
                needs_confirmation=False,
                field_status=FieldStatus.MISSING,
                system_message="Implement ldmf handler",
                next_expected_field=None,
            )


class DateIntervalsHandler(BaseFieldHandler):
    """Handler for RequiredField.ENGAGEMENT_DATES."""

    def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> FieldHandlerResponse:
        return FieldHandlerResponse(
                needs_confirmation=False,
                field_status=FieldStatus.MISSING,
                system_message="Implement date intervals handler",
                next_expected_field=None,
            )


class ObjectiveHandler(BaseFieldHandler):
    """Handler for RequiredField.OBJECTIVE_SCOPE."""

    def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> FieldHandlerResponse:
        return FieldHandlerResponse(
                needs_confirmation=False,
                field_status=FieldStatus.MISSING,
                system_message="Implement objective and scope handler",
                next_expected_field=None,
            )


class OutcomesHandler(BaseFieldHandler):
    """Handler for RequiredField.OUTCOMES."""

    def check_and_get_response(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData
    ) -> FieldHandlerResponse:
        # Check if outcomes are confirmed
        if confirmed_data.outcomes is not None:
            return FieldHandlerResponse(
                needs_confirmation=False,
                system_message=None,
                next_expected_field=None,
                field_status=FieldStatus.CONFIRMED,
            )
        # Check if outcomes are in aggregated data
        elif aggregated_data.outcomes is not None:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.SINGLE,
                system_message=f"found this outcomes: {aggregated_data.outcomes} can you confirm?",
                next_expected_field=None,
            )
        # Neither confirmed nor aggregated
        else:
            return FieldHandlerResponse(
                needs_confirmation=True,
                field_status=FieldStatus.MISSING,
                system_message="NEED_INFO_OUTCOMES",
                next_expected_field=None,
            )
