# OutcomesHandler and ExtractedDataService Integration Tests

This document describes the comprehensive integration tests for the OutcomesHandler and ExtractedDataService components, focusing on the missing data detection flow and progressive qual data collection.

## Test Overview

The test suite covers:

1. **OutcomesHandler Unit Tests** - Specific handler logic testing
2. **ExtractedDataService Integration Tests** - Service-level integration with real database
3. **End-to-End Message Flow Tests** - Complete user message → system response flow
4. **Three-Scenario Handling Tests** - Zero/one/multiple values pattern testing
5. **Document Processing Integration Tests** - Azure Blob Storage and file handling
6. **Error Handling and Edge Cases** - Robustness testing

## Test Setup Requirements

### Prerequisites

1. **Test Database**: Uses real database connections with 'test_' prefix
2. **Azure Blob Storage**: Uses real Azure Blob Storage connections for document handling
3. **Mocked OpenAI API**: OpenAI API calls are mocked to avoid external API costs
4. **Environment**: Must run with `ENVIRONMENT=test`

### Running Tests

From the `app` folder:

```bash
# Run all integration tests
ENVIRONMENT=test pytest -s -v tests/test_outcomes_handler_integration.py tests/test_extracted_data_service_blob_integration.py

# Run specific test class
ENVIRONMENT=test pytest -s -v tests/test_outcomes_handler_integration.py::TestOutcomesHandlerUnit

# Use the test runner script
ENVIRONMENT=test python tests/test_outcomes_handler_runner.py

# Run specific test class with runner
ENVIRONMENT=test python tests/test_outcomes_handler_runner.py --test-class TestOutcomesHandlerUnit
```

## Test Structure

### 1. OutcomesHandler Unit Tests (`TestOutcomesHandlerUnit`)

Tests the core OutcomesHandler logic in isolation:

- **`test_outcomes_already_confirmed`**: When outcomes are confirmed → CONFIRMED status
- **`test_outcomes_in_aggregated_data_not_confirmed`**: When outcomes exist but need confirmation → SINGLE status
- **`test_outcomes_missing_entirely`**: When outcomes are missing → MISSING status with "NEED_INFO_OUTCOMES"
- **`test_confirmed_data_overrides_aggregated_data`**: Confirmed data takes precedence

### 2. ExtractedDataService Integration Tests (`TestExtractedDataServiceIntegration`)

Tests service-level integration with real database:

- **`test_missing_data_detection_outcomes_missing`**: Complete missing data detection
- **`test_missing_data_detection_outcomes_in_aggregated_data`**: Confirmation flow
- **`test_missing_data_detection_outcomes_confirmed`**: Data complete scenario
- **`test_progressive_data_collection_flow`**: Multi-step confirmation process

### 3. End-to-End Message Flow Tests (`TestEndToEndMessageFlow`)

Tests complete user interaction flow:

- **`test_uncertain_intent_triggers_missing_data_detection`**: Intent classification → missing data detection
- **`test_conversation_state_tracking`**: Conversation state updates during collection
- **`test_confirmed_data_storage_and_retrieval`**: JSON field storage in database

### 4. Three-Scenario Handling Tests (`TestThreeScenarioHandling`)

Tests the three-scenario pattern for outcomes field:

- **`test_zero_values_scenario`**: No outcomes data available
- **`test_one_value_scenario`**: Single outcomes value needs confirmation
- **`test_multiple_values_scenario_priority_override`**: Multiple sources with priority-based selection

### 5. Document Processing Integration Tests (`TestDocumentProcessingIntegration`)

Tests Azure Blob Storage and document handling:

- **`test_document_upload_and_extraction_flow`**: File upload → extraction → missing data detection
- **`test_message_with_file_attachment_flow`**: Message with file attachment processing
- **`test_multiple_document_sources_aggregation`**: Data aggregation from multiple sources
- **`test_blob_storage_naming_conventions`**: Azure Blob Storage naming patterns

### 6. Error Handling Tests (`TestErrorHandlingAndEdgeCases`)

Tests robustness and error scenarios:

- **`test_nonexistent_conversation_id`**: Graceful handling of invalid conversation IDs
- **`test_malformed_confirmed_data_json`**: JSON parsing error handling

## Key Test Patterns

### Real Database Integration

Tests use real database connections through dependency injection:

```python
@pytest.fixture
async def extracted_data_service_real(
    self, extracted_data_repository_dep: ExtractedDataRepositoryDep
):
    """Fixture providing real ExtractedDataService with database connection."""
    from services import ExtractedDataService
    return ExtractedDataService(extracted_data_repository=extracted_data_repository_dep)
```

### Mocked External APIs

OpenAI API calls are mocked to avoid costs:

```python
with patch(
    'services.conversation_message.ConversationMessageProcessor._get_intent',
    return_value=ConversationMessageIntention.UNCERTAINITY
):
    # Test code here
```

### Test Data Creation

Tests create realistic test data:

```python
extracted_data = ExtractedData.create(
    conversation_id=test_conversation_id,
    data_source_type=DataSourceType.KX_DASH
)
extracted_data.outcomes = "Test outcomes from KX Dash"
extracted_data.client_name = json.dumps(["Test Client"])
```

### Progressive Testing

Tests verify step-by-step data collection:

```python
# Step 1: No confirmed data
confirmed_data = ConfirmedData()
response = await service.get_missing_required_data_prompts(...)
assert response.conversation_state == ConversationState.COLLECTING_CLIENT_NAME

# Step 2: Confirm client name
confirmed_data.client_name = "Test Client"
response = await service.get_missing_required_data_prompts(...)
assert response.conversation_state == ConversationState.COLLECTING_COUNTRY
```

## Test Data Scenarios

### Outcomes Field Testing

The tests cover all possible outcomes field scenarios:

1. **Missing**: No outcomes data in any source
2. **Single Value**: One outcomes value from one source
3. **Multiple Values**: Outcomes from multiple sources (priority-based selection)
4. **Confirmed**: User has already confirmed outcomes

### Data Source Priority

Tests verify correct priority handling:

1. **KX_DASH** (highest priority)
2. **DOCUMENTS** (medium priority)  
3. **PROMPT** (lowest priority)

### Conversation States

Tests verify proper state transitions:

- `INITIAL` → `COLLECTING_CLIENT_NAME` → `COLLECTING_COUNTRY` → `COLLECTING_DATES` → `COLLECTING_OBJECTIVE` → `COLLECTING_OUTCOMES` → `DATA_COMPLETE`

## Expected Test Results

When running the tests, you should see:

1. **Unit Tests**: Fast execution, testing handler logic
2. **Integration Tests**: Database operations, realistic data flows
3. **End-to-End Tests**: Complete user interaction simulation
4. **Document Tests**: File upload and processing simulation
5. **Error Tests**: Graceful error handling verification

## Troubleshooting

### Common Issues

1. **Environment Variable**: Ensure `ENVIRONMENT=test` is set
2. **Database Connection**: Verify test database is accessible
3. **Azure Blob Storage**: Check Azure connection string configuration
4. **Dependencies**: Ensure all test dependencies are installed

### Debug Mode

Run tests with additional debugging:

```bash
ENVIRONMENT=test pytest -s -v --tb=long tests/test_outcomes_handler_integration.py
```

### Test Isolation

Each test creates its own conversation and cleans up after itself. Tests should be able to run in any order and in parallel.

## Coverage Areas

The tests provide comprehensive coverage of:

- ✅ OutcomesHandler logic (all scenarios)
- ✅ ExtractedDataService integration
- ✅ Database operations (real connections)
- ✅ Conversation state management
- ✅ Progressive data collection
- ✅ Azure Blob Storage integration
- ✅ Error handling and edge cases
- ✅ Three-scenario handling pattern
- ✅ Priority-based data aggregation
- ✅ JSON field storage and retrieval
