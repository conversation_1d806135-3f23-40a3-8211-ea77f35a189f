from datetime import date
import json

from constants.extracted_data import ConversationState, DataSourceType, MissingDataStatus
from schemas import ExtractedData
from schemas.confirmed_data import ConfirmedData


class TestDocumentProcessingIntegration:
    """Integration tests for document processing with Azure Blob Storage."""


    async def test_and_extraction_flow(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real,
    ):
        # Simulate document processing results by creating extracted data
        # This would normally be done by Azure Durable Functions
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id,
            data_source_type=DataSourceType.DOCUMENTS
        )
        extracted_data.outcomes = "Extracted outcomes from document processing"
        extracted_data.client_name = json.dumps(["Document Client"])
        extracted_data.objective_and_scope = "Document objective and scope"
        
        # Save extracted data to database
        await extracted_data_repository_real.update(extracted_data)

        # Test missing data detection with document-extracted data
        confirmed_data = ConfirmedData(
            client_name="Document Client",
            ldmf_country="Document Country",
            date_intervals=("2023-01-01", "2023-12-31"),
            objective_and_scope="Document objective and scope"
            # outcomes left None to test confirmation flow
        )
        
        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id,
            confirmed_data=confirmed_data
        )

        # Should detect missing country first (based on field collection order)
        assert response.status == MissingDataStatus.MISSING_DATA
        assert response.conversation_state == ConversationState.COLLECTING_OUTCOMES

    async def test_multiple_document_sources_aggregation(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real,
    ):
        """Test aggregation of data from multiple document sources."""
        # Create extracted data from documents source
        documents_data = ExtractedData.create(
            conversation_id=test_conversation_id,
            data_source_type=DataSourceType.DOCUMENTS
        )
        documents_data.outcomes = "Outcomes from document analysis"
        documents_data.client_name = json.dumps(["Document Client A", "Document Client B"])
        await extracted_data_repository_real.update(documents_data)

        # Create extracted data from KX Dash source (higher priority)
        kx_dash_data = ExtractedData.create(
            conversation_id=test_conversation_id,
            data_source_type=DataSourceType.KX_DASH
        )
        kx_dash_data.outcomes = "Outcomes from KX Dash (should override)"
        kx_dash_data.client_name = json.dumps(["KX Client"])
        await extracted_data_repository_real.update(kx_dash_data)

        # Test aggregation
        aggregated_data = await extracted_data_service_real.aggregate_data(test_conversation_id)
        
        # Client names should be merged from all sources
        expected_client_names = sorted(["Document Client A", "Document Client B", "KX Client"])
        assert aggregated_data.client_name == expected_client_names
        
        # Outcomes should use highest priority source (KX_DASH)
        assert aggregated_data.outcomes == "Outcomes from KX Dash (should override)"

    async def test_document_extraction_error_handling(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real,
    ):
        """Test handling of document extraction errors."""
        # Create extracted data with partial information (simulating extraction failure)
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id,
            data_source_type=DataSourceType.DOCUMENTS
        )
        # Only set some fields, leaving outcomes None (simulating extraction failure)
        extracted_data.client_name = json.dumps(["Partial Client"])
        # outcomes intentionally left None
        
        await extracted_data_repository_real.update(extracted_data)

        # Test missing data detection with partial extraction
        confirmed_data = ConfirmedData(
            client_name="Partial Client"
        )
        
        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id,
            confirmed_data=confirmed_data
        )

        # Should detect missing country first
        assert response.status == MissingDataStatus.MISSING_DATA
        assert response.conversation_state == ConversationState.COLLECTING_OUTCOMES
        
        # Should include outcomes in missing fields list
        assert "outcomes" in response.missing_fields


class TestDurableFunctionsIntegration:
    """Integration tests for Azure Durable Functions integration."""

    async def test_document_processing_workflow_simulation(
        self,
        test_conversation_id,
        extracted_data_repository_real,
    ):
        """Test simulation of document processing workflow from Durable Functions."""
        # Simulate the workflow: Document uploaded → Extraction → Chunking → Data stored
        
        # Step 1: Simulate document extraction results
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id,
            data_source_type=DataSourceType.DOCUMENTS
        )
        
        # Simulate Document Intelligence extraction results
        extracted_data.client_name = json.dumps(["Extracted Client Name"])
        extracted_data.ldmf_country = json.dumps(["Extracted Country"])
        extracted_data.outcomes = "Extracted outcomes from Document Intelligence"
        extracted_data.objective_and_scope = "Extracted objective and scope"
        extracted_data.start_date = date(2023, 6, 1)
        extracted_data.end_date = date(2023, 12, 31)
        
        # Save extraction results
        await extracted_data_repository_real.update(extracted_data)
        
        # Step 2: Verify data was stored correctly
        retrieved_data = await extracted_data_repository_real.get(
            conversation_id=test_conversation_id,
            data_source_type=DataSourceType.DOCUMENTS
        )
        
        assert retrieved_data is not None
        assert retrieved_data.outcomes == "Extracted outcomes from Document Intelligence"
        assert retrieved_data.client_name == extracted_data.client_name
        assert retrieved_data.ldmf_country == extracted_data.ldmf_country
        assert retrieved_data.start_date == date(2023, 6, 1)
        assert retrieved_data.end_date == date(2023, 12, 31)

    async def test_prompt_processing_workflow_simulation(
        self,
        test_conversation_id,
        extracted_data_repository_real,
    ):
        """Test simulation of prompt processing workflow from Durable Functions."""
        # Simulate prompt processing: User message → LLM processing → Data extraction → Storage
        
        # Step 1: Simulate prompt-based extraction results
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id,
            data_source_type=DataSourceType.PROMPT
        )
        
        # Simulate LLM extraction from user messages
        extracted_data.client_name = json.dumps(["Prompt Client"])
        extracted_data.outcomes = "Outcomes extracted from user conversation"
        extracted_data.objective_and_scope = "Objective extracted from user messages"
        
        # Save extraction results
        await extracted_data_repository_real.update(extracted_data)
        
        # Step 2: Verify data was stored correctly
        retrieved_data = await extracted_data_repository_real.get(
            conversation_id=test_conversation_id,
            data_source_type=DataSourceType.PROMPT
        )
        
        assert retrieved_data is not None
        assert retrieved_data.outcomes == extracted_data.outcomes
        assert retrieved_data.client_name == extracted_data.client_name
        assert retrieved_data.objective_and_scope == extracted_data.objective_and_scope
