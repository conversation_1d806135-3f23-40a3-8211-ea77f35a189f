#!/usr/bin/env python3
"""
Test runner script for OutcomesHandler and ExtractedDataService integration tests.

This script provides a convenient way to run the comprehensive integration tests
with proper environment setup and configuration.

Usage:
    From the app folder:
    ENVIRONMENT=test python tests/test_outcomes_handler_runner.py

    Or run specific test classes:
    ENVIRONMENT=test python tests/test_outcomes_handler_runner.py --test-class TestOutcomesHandlerUnit
    ENVIRONMENT=test python tests/test_outcomes_handler_runner.py --test-class TestExtractedDataServiceIntegration
    ENVIRONMENT=test python tests/test_outcomes_handler_runner.py --test-class TestEndToEndMessageFlow
"""

import argparse
import asyncio
import os
from pathlib import Path
import sys


# Add the app directory to Python path
app_dir = Path(__file__).parent.parent
sys.path.insert(0, str(app_dir))


def setup_test_environment():
    """Setup test environment variables and configuration."""
    # Ensure we're running in test environment
    if os.environ.get('ENVIRONMENT') != 'test':
        print("ERROR: Tests must be run with ENVIRONMENT=test")
        print("Usage: ENVIRONMENT=test python tests/test_outcomes_handler_runner.py")
        sys.exit(1)
    
    # Set additional test-specific environment variables if needed
    test_env_vars = {
        'PYTHONPATH': str(app_dir),
        'PYTEST_CURRENT_TEST': 'true',
    }
    
    for key, value in test_env_vars.items():
        if key not in os.environ:
            os.environ[key] = value


def run_pytest_command(test_class=None, verbose=True, capture=False):
    """Run pytest with appropriate arguments."""
    import subprocess
    
    cmd = ['python', '-m', 'pytest']
    
    if verbose:
        cmd.extend(['-v', '-s'])
    
    if not capture:
        cmd.append('--tb=short')
    
    # Add specific test files
    test_files = [
        'tests/test_outcomes_handler_integration.py',
        'tests/test_extracted_data_service_blob_integration.py'
    ]
    
    if test_class:
        # Run specific test class
        for test_file in test_files:
            cmd.append(f'{test_file}::{test_class}')
    else:
        # Run all test files
        cmd.extend(test_files)
    
    print(f"Running command: {' '.join(cmd)}")
    print("-" * 80)
    
    try:
        result = subprocess.run(cmd, cwd=app_dir, check=False)
        return result.returncode
    except KeyboardInterrupt:
        print("\nTest execution interrupted by user")
        return 1
    except Exception as e:
        print(f"Error running tests: {e}")
        return 1


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(
        description="Run OutcomesHandler and ExtractedDataService integration tests"
    )
    parser.add_argument(
        '--test-class',
        help='Run specific test class (e.g., TestOutcomesHandlerUnit)',
        default=None
    )
    parser.add_argument(
        '--quiet',
        action='store_true',
        help='Run tests in quiet mode (less verbose output)'
    )
    parser.add_argument(
        '--capture',
        action='store_true',
        help='Capture output (don\'t show print statements during tests)'
    )
    
    args = parser.parse_args()
    
    # Setup test environment
    setup_test_environment()
    
    print("=" * 80)
    print("OutcomesHandler and ExtractedDataService Integration Tests")
    print("=" * 80)
    print(f"Environment: {os.environ.get('ENVIRONMENT')}")
    print(f"Working directory: {app_dir}")
    
    if args.test_class:
        print(f"Running test class: {args.test_class}")
    else:
        print("Running all integration tests")
    
    print("=" * 80)
    
    # Run tests
    exit_code = run_pytest_command(
        test_class=args.test_class,
        verbose=not args.quiet,
        capture=args.capture
    )
    
    print("=" * 80)
    if exit_code == 0:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
    print("=" * 80)
    
    return exit_code


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
