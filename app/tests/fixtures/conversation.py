from uuid import UUID

from fastapi import status
from httpx import AsyncClient
import pytest

from constants.operation_ids import operation_ids


__all__ = ['conversation_data', 'test_conversation_id']


@pytest.fixture(scope='session')
def conversation_data():
    """Fixture that provides standard test conversation data."""
    return {
        'from_dash': True,
    }

@pytest.fixture
async def test_conversation_id(
    auth_mock,
    auth_header,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    """Create a test conversation and return its ID."""
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    response = await async_client.post(conversation_url, headers=auth_header, json=conversation_data)
    print(response.json())
    assert response.status_code == status.HTTP_201_CREATED
    return UUID(response.json()['conversation']['id'])