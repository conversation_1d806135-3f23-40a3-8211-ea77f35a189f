from datetime import date, datetime, timezone
import json
from unittest.mock import AsyncMock
from uuid import UUID

import pytest

from constants.extracted_data import DataSourceType
from repositories import ConversationRepository, ExtractedDataRepository
from schemas import ExtractedData
from services import ExtractedDataService


__all__ = [
    'extracted_data_repository',
    'extracted_data_service',
    'kx_dash_extracted_data',
    'documents_extracted_data',
    'prompt_extracted_data',
    'extracted_data_repository_real',
    'extracted_data_service_real',
]


@pytest.fixture
def extracted_data_repository():
    return AsyncMock()


@pytest.fixture
def extracted_data_service(extracted_data_repository):
    return ExtractedDataService(extracted_data_repository=extracted_data_repository)


@pytest.fixture
def kx_dash_extracted_data():
    return ExtractedData(
        ConversationPublicId=UUID('00000000-0000-0000-0000-000000000001'),
        DataSourceType=DataSourceType.KX_DASH,
        CreatedAt=datetime.now(timezone.utc),
        ActivityName='KX Activity',
        ClientName=json.dumps(['KX Client']),  # type: ignore
        LDMFCountry=json.dumps(['KX Country']),  # type: ignore
        Title='KX Title',
        StartDate=date(2023, 1, 1),
        EndDate=date(2023, 12, 31),
        ClientIndustry=json.dumps({'level_1': 'KX Industry'}),
        ClientServices=json.dumps({'level_1': 'KX Service'}),
        TeamAndRoles=json.dumps({'team_members': ['KX Team Member']}),
        ObjectiveAndScope='KX Objective and Scope',
        Outcomes='KX Outcomes',
    )


@pytest.fixture
def documents_extracted_data():
    return ExtractedData(
        ConversationPublicId=UUID('00000000-0000-0000-0000-000000000001'),
        DataSourceType=DataSourceType.DOCUMENTS,
        CreatedAt=datetime.now(timezone.utc),
        ActivityName='Document Activity',
        ClientName=json.dumps(['Document Client']),  # type: ignore
        LDMFCountry=json.dumps(['Document Country']),  # type: ignore
        Title='Document Title',
        StartDate=date(2023, 2, 1),
        EndDate=date(2023, 11, 30),
        ClientIndustry=json.dumps({'level_1': 'Document Industry'}),
        ClientServices=json.dumps({'level_1': 'Document Service'}),
        TeamAndRoles=json.dumps({'team_members': ['Document Team Member']}),
        ObjectiveAndScope='Document Objective and Scope',
        Outcomes='Document Outcomes',
    )


@pytest.fixture
def prompt_extracted_data():
    return ExtractedData(
        ConversationPublicId=UUID('00000000-0000-0000-0000-000000000001'),
        DataSourceType=DataSourceType.PROMPT,
        CreatedAt=datetime.now(timezone.utc),
        ActivityName='Prompt Activity',
        ClientName=json.dumps(['Prompt Client']),  # type: ignore
        LDMFCountry=json.dumps(['Prompt Country']),  # type: ignore
        Title='Prompt Title',
        StartDate=date(2023, 3, 1),
        EndDate=date(2023, 10, 31),
        ClientIndustry=json.dumps({'level_1': 'Prompt Industry'}),
        ClientServices=json.dumps({'level_1': 'Prompt Service'}),
        TeamAndRoles=json.dumps({'team_members': ['Prompt Team Member']}),
        ObjectiveAndScope='Prompt Objective and Scope',
        Outcomes='Prompt Outcomes',
    )


@pytest.fixture
def extracted_data_repository_real(db_session):
    conversation_repository = ConversationRepository(db_session=db_session)
    return ExtractedDataRepository(db_session=db_session, conversation_repository=conversation_repository)


@pytest.fixture
def extracted_data_service_real(extracted_data_repository_real):
    return ExtractedDataService(extracted_data_repository=extracted_data_repository_real)