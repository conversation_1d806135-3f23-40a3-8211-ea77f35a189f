import json
import logging
from pathlib import Path

from core.enum import StrEnum


__all__ = [
    'MessageRole',
    'MessageType',
    'OptionType',
    'WELCOME_MESSAGE',
    'ConversationMessageIntention',
    'DASH_TASK_SELECTED_TEMPLATE',
    'INTENT_OBJECT_STRUCTURE',
    'GENERAL_INTENTIONS',
    'BRIEF_DESCRIPTION_REPLY',
    'EXAMPLE_REPLY',
    'EXTRACT_DATA_SYSTEM_PROMPT',
    'EXTRACT_DATA_USER_PROMPT',
    'NEED_INFO_OUTCOMES',
]


logger = logging.getLogger(__name__)


class MessageRole(StrEnum):
    SYSTEM = 'system'
    USER = 'user'


class MessageType(StrEnum):
    TEXT = 'text'
    FILE = 'file'
    FORM = 'form'
    TEXT_WITH_FILE = 'text_with_file'


def _load_file_from_folder(folder: str, file_name: str) -> str:
    path = Path(__file__).parent.parent / 'assets' / folder / file_name
    return path.read_text(encoding='utf-8').strip()


WELCOME_MESSAGE = _load_file_from_folder('conversation_messages', 'welcome_message.txt')
DASH_TASK_SELECTED_TEMPLATE = _load_file_from_folder('conversation_messages', 'dash_task_selected.txt')
INTENT_OBJECT_STRUCTURE = _load_file_from_folder('intentions', 'intent_object_structure.txt')
GENERAL_INTENTIONS = json.loads(_load_file_from_folder('intentions', 'general_intentions.json'))
BRIEF_DESCRIPTION_REPLY = _load_file_from_folder('conversation_messages', 'brief_description_reply.txt')
UNDEFINED_REPLY = _load_file_from_folder('conversation_messages', 'undefined_reply.txt')
EXAMPLE_REPLY = _load_file_from_folder('conversation_messages', 'example_reply.txt')
EXTRACT_DATA_SYSTEM_PROMPT = _load_file_from_folder('extract_data_prompts', 'extract_data_system_prompt.txt')
EXTRACT_DATA_USER_PROMPT = _load_file_from_folder('extract_data_prompts', 'extract_data_user_prompt.txt')

NEED_INFO_OUTCOMES = _load_file_from_folder('conversation_messages', 'need_info_outcomes.txt')


class OptionType(StrEnum):
    DATES = 'dates'
    LDMF_COUNTRY = 'ldmf_country'
    CLIENT_NAME = 'client_name'
    KX_DASH_TASK = 'kx_dash_task'


class ConversationMessageIntention(StrEnum):
    UNDEFINED = 'undefined'

    GENERATE_QUAL = 'generate_qual'
    EXTRACTION = 'extraction'
    EXAMPLE = 'example'
    DASH_DISCARD = 'dash_discard'
    UNCERTAINITY = 'uncertainty'  # NOTE: used to be BRIEF_DESCRIPTION, renamed for better LLM understanding
    NEED_CONTEXT = 'need_context'  # NOTE: mapped to UNCERTAINTY
